{"rustc": 8210029788606052455, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 17342157952639649116, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 16031280701877081082], [1238778183371849706, "yaml_rust2", false, 5655302290766322628], [2244620803250265856, "ron", false, 655278211234164303], [2356429411733741858, "ini", false, 14678906712247155337], [6517602928339163454, "path<PERSON><PERSON>", false, 14223273039827007757], [9689903380558560274, "serde", false, 16089468792489217048], [11946729385090170470, "async_trait", false, 15387976393327610615], [13475460906694513802, "convert_case", false, 17109710977024207703], [14718834678227948963, "winnow", false, 10004585794444425361], [15367738274754116744, "serde_json", false, 13605883423583756334], [15609422047640926750, "toml", false, 4656937562826605729]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/config-ef30e25eba3a1ceb/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
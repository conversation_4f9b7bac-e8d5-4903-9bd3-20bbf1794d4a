{"rustc": 8210029788606052455, "features": "[\"aws-lc-sys\", \"prebuilt-nasm\"]", "declared_features": "[\"alloc\", \"asan\", \"aws-lc-sys\", \"bindgen\", \"default\", \"fips\", \"non-fips\", \"prebuilt-nasm\", \"ring-io\", \"ring-sig-verify\", \"test_logging\", \"unstable\"]", "target": 18300691495230371829, "profile": 2040997289075261528, "path": 2251881813140144793, "deps": [[125823485620238427, "build_script_build", false, 8388877597267646256], [6528079939221783635, "zeroize", false, 17369025753398953883], [7901501397033386043, "aws_lc_sys", false, 3604712984065346241]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/aws-lc-rs-0a6283a5ae8a8d0b/dep-lib-aws_lc_rs", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
{"rustc": 8210029788606052455, "features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"default\", \"log\", \"logging\", \"prefer-post-quantum\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 16207314485680614956, "deps": [[125823485620238427, "aws_lc_rs", false, 17626276240909795857], [2883436298747778685, "pki_types", false, 12536536791500185010], [3722963349756955755, "once_cell", false, 5255602016566877350], [5491919304041016563, "ring", false, 13145302980176438766], [5986029879202738730, "log", false, 4400463571684825790], [6528079939221783635, "zeroize", false, 17369025753398953883], [7161480121686072451, "build_script_build", false, 8674607546253455610], [17003143334332120809, "subtle", false, 7144376990292349833], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 4603720386298087113]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustls-17bbeb2d0d81b5ac/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
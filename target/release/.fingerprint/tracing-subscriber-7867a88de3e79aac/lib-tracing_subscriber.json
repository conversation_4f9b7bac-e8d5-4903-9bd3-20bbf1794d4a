{"rustc": 8210029788606052455, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 10369491684090452477, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 3448468943500793539], [1017461770342116999, "sharded_slab", false, 13641030723918027258], [3424551429995674438, "tracing_core", false, 13742787994403531972], [3666196340704888985, "smallvec", false, 9292582353040258527], [3722963349756955755, "once_cell", false, 5255602016566877350], [6981130804689348050, "tracing_serde", false, 12339417582399837333], [8606274917505247608, "tracing", false, 1687055729262555498], [8614575489689151157, "nu_ansi_term", false, 4311048069293464461], [9451456094439810778, "regex", false, 13478192934701401379], [9689903380558560274, "serde", false, 16089468792489217048], [10806489435541507125, "tracing_log", false, 12017327465331350218], [12427285511609802057, "thread_local", false, 7453708957590853040], [15367738274754116744, "serde_json", false, 13605883423583756334]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tracing-subscriber-7867a88de3e79aac/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
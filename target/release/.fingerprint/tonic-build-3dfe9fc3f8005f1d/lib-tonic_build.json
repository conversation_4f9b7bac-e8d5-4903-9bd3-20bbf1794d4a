{"rustc": 8210029788606052455, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 12990859512634467869, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 4376802947340442664], [3060637413840920116, "proc_macro2", false, 407421095309152804], [8549471757621926118, "prettyplease", false, 1155631309739137892], [16470553738848018267, "prost_types", false, 5049133220648319012], [17990358020177143287, "quote", false, 8081814984997477497], [18149961000318489080, "syn", false, 9454978440104916550]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tonic-build-3dfe9fc3f8005f1d/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
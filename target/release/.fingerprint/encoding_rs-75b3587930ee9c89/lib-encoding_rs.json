{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 2040997289075261528, "path": 17640807469601094085, "deps": [[10411997081178400487, "cfg_if", false, 4402024493393318529]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/encoding_rs-75b3587930ee9c89/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
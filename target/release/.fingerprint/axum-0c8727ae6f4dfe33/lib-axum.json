{"rustc": 8210029788606052455, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 11385615876995111466, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 9875549795986466681], [784494742817713399, "tower_service", false, 15407990804927376824], [1906322745568073236, "pin_project_lite", false, 12290216111308540520], [2517136641825875337, "sync_wrapper", false, 9137233696260318329], [3129130049864710036, "memchr", false, 8337254613841808669], [5695049318159433696, "tower", false, 909093847508709101], [7695812897323945497, "itoa", false, 296690650185676511], [7712452662827335977, "tower_layer", false, 8534028178987104139], [7858942147296547339, "rustversion", false, 149866773187821088], [8913795983780778928, "matchit", false, 10440455874519196234], [9010263965687315507, "http", false, 9479401508535735952], [9689903380558560274, "serde", false, 16089468792489217048], [10229185211513642314, "mime", false, 6041318374769966137], [10629569228670356391, "futures_util", false, 11930344337119092351], [14084095096285906100, "http_body", false, 13067156537629412690], [15176407853393882315, "axum_core", false, 17463633384120625638], [16066129441945555748, "bytes", false, 11087883134660439610], [16900715236047033623, "http_body_util", false, 10582451409465306697]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-0c8727ae6f4dfe33/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
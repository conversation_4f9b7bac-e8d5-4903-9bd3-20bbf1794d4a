{"rustc": 8210029788606052455, "features": "[\"alloc\", \"aws-lc-rs\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 2040997289075261528, "path": 1517439749105498433, "deps": [[125823485620238427, "aws_lc_rs", false, 17626276240909795857], [2883436298747778685, "pki_types", false, 12536536791500185010], [5491919304041016563, "ring", false, 13145302980176438766], [8995469080876806959, "untrusted", false, 2820115043527206495]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustls-webpki-97197aa0d6cbc3c7/dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}
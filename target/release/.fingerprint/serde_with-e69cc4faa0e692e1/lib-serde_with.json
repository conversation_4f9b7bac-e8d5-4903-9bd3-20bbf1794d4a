{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5456902659710135487, "path": 10990378695296491665, "deps": [[6158493786865284961, "serde_with_macros", false, 3196889208964221165], [9689903380558560274, "serde", false, 16089468792489217048], [16257276029081467297, "serde_derive", false, 6206809494424191035]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/serde_with-e69cc4faa0e692e1/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}